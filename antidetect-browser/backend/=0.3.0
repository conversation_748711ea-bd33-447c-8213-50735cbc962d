Collecting camoufox
  Downloading camoufox-0.4.11-py3-none-any.whl (71 kB)
Collecting browserforge<2.0.0,>=1.2.1
  Using cached browserforge-1.2.3-py3-none-any.whl (39 kB)
Collecting playwright
  Downloading playwright-1.54.0-py3-none-macosx_11_0_arm64.whl (38.7 MB)
Collecting pysocks
  Using cached PySocks-1.7.1-py3-none-any.whl (16 kB)
Collecting platformdirs
  Using cached platformdirs-4.3.8-py3-none-any.whl (18 kB)
Requirement already satisfied: requests in ./venv/lib/python3.9/site-packages (from camoufox) (2.32.4)
Collecting screeninfo
  Using cached screeninfo-0.8.1-py3-none-any.whl (12 kB)
Requirement already satisfied: pyyaml in ./venv/lib/python3.9/site-packages (from camoufox) (6.0.2)
Collecting lxml
  Using cached lxml-6.0.0-cp39-cp39-macosx_10_9_universal2.whl (8.4 MB)
Requirement already satisfied: click in ./venv/lib/python3.9/site-packages (from camoufox) (8.1.8)
Requirement already satisfied: numpy in ./venv/lib/python3.9/site-packages (from camoufox) (2.0.2)
Collecting orjson
  Downloading orjson-3.11.1-cp39-cp39-macosx_10_15_x86_64.macosx_11_0_arm64.macosx_10_15_universal2.whl (241 kB)
Collecting ua_parser
  Using cached ua_parser-1.0.1-py3-none-any.whl (31 kB)
Collecting tqdm
  Using cached tqdm-4.67.1-py3-none-any.whl (78 kB)
Requirement already satisfied: typing_extensions in ./venv/lib/python3.9/site-packages (from camoufox) (4.14.1)
Collecting language-tags
  Using cached language_tags-1.2.0-py3-none-any.whl (213 kB)
Collecting pyee<14,>=13
  Using cached pyee-13.0.0-py3-none-any.whl (15 kB)
Requirement already satisfied: greenlet<4.0.0,>=3.1.1 in ./venv/lib/python3.9/site-packages (from playwright->camoufox) (3.2.4)
Requirement already satisfied: certifi>=2017.4.17 in ./venv/lib/python3.9/site-packages (from requests->camoufox) (2025.8.3)
Requirement already satisfied: charset_normalizer<4,>=2 in ./venv/lib/python3.9/site-packages (from requests->camoufox) (3.4.3)
Requirement already satisfied: idna<4,>=2.5 in ./venv/lib/python3.9/site-packages (from requests->camoufox) (3.10)
Requirement already satisfied: urllib3<3,>=1.21.1 in ./venv/lib/python3.9/site-packages (from requests->camoufox) (2.5.0)
Collecting Cython
  Using cached cython-3.1.2-cp39-cp39-macosx_11_0_arm64.whl (2.8 MB)
Collecting pyobjc-framework-Cocoa
  Using cached pyobjc_framework_cocoa-11.1-cp39-cp39-macosx_10_9_universal2.whl (388 kB)
Collecting pyobjc-core>=11.1
  Using cached pyobjc_core-11.1-cp39-cp39-macosx_10_9_universal2.whl (677 kB)
Collecting ua-parser-builtins
  Using cached ua_parser_builtins-0.18.0.post1-py3-none-any.whl (86 kB)
Installing collected packages: pyobjc-core, ua-parser-builtins, pyobjc-framework-Cocoa, pyee, Cython, ua-parser, tqdm, screeninfo, pysocks, playwright, platformdirs, orjson, lxml, language-tags, browserforge, camoufox
Successfully installed Cython-3.1.2 browserforge-1.2.3 camoufox-0.4.11 language-tags-1.2.0 lxml-6.0.0 orjson-3.11.1 platformdirs-4.3.8 playwright-1.54.0 pyee-13.0.0 pyobjc-core-11.1 pyobjc-framework-Cocoa-11.1 pysocks-1.7.1 screeninfo-0.8.1 tqdm-4.67.1 ua-parser-1.0.1 ua-parser-builtins-0.18.0.post1
