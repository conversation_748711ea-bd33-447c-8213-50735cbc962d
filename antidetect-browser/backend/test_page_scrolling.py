#!/usr/bin/env python3
"""
Test Facebook page scrolling with new XPath selectors
"""

import asyncio
import sys
import json
from pathlib import Path

async def test_page_scrolling():
    """Test page scrolling functionality"""
    print("\n🦊 Testing Facebook Page Scrolling with Camoufox")
    print("=" * 60)
    
    try:
        from app.services.facebook_scraper import FacebookScraper
        from app.models.scraping import ScrapingType
        
        # Create scraper instance
        scraper = FacebookScraper()
        print("✅ FacebookScraper created")
        
        # Check page selectors
        print("\n🔍 Page Selectors Configuration:")
        for key, value in scraper.page_selectors.items():
            if len(str(value)) > 80:
                print(f"   {key}: {str(value)[:80]}...")
            else:
                print(f"   {key}: {value}")
        
        # Test profile creation for scraping
        profile_data = {
            'name': 'Page Scrolling Test Profile',
            'proxy_config': {
                'type': 'no_proxy',
                'host': None,
                'port': None,
                'username': None,
                'password': None
            }
        }
        
        print("\n📝 Creating test profile...")
        profile_result = await scraper.profile_manager.create_profile(profile_data)
        
        if not profile_result.get('success'):
            print(f"❌ Profile creation failed: {profile_result.get('message')}")
            return False
        
        profile_path = profile_result.get('profile_path')
        import os
        profile_id = os.path.basename(profile_path)
        
        print(f"✅ Profile created: {profile_path}")
        
        # Test browser launch
        print("\n🚀 Testing browser launch...")
        from app.services.camoufox_manager import CamoufoxBrowserManager
        
        camoufox_manager = CamoufoxBrowserManager()
        
        # Launch browser
        launch_result = await camoufox_manager.launch_browser(
            profile_id=profile_id,
            profile_path=profile_path,
            proxy_config={'type': 'no_proxy'},
            fingerprint={'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0'},
            headless=False
        )
        
        if not launch_result.get('success'):
            print(f"❌ Browser launch failed: {launch_result.get('message')}")
            return False
        
        context = await camoufox_manager.get_browser_context(profile_id)
        if not context:
            print("❌ Failed to get browser context")
            return False
        
        print("✅ Browser launched successfully")
        
        # Test Facebook post URL
        test_post_url = input("\n📝 Enter Facebook post URL to test (or press Enter for demo): ").strip()
        
        if not test_post_url:
            test_post_url = "https://www.facebook.com/groups/591054007361950/posts/1234567890/"
            print(f"Using demo URL: {test_post_url}")
        
        # Test page scrolling
        print(f"\n🔄 Testing page scrolling for: {test_post_url}")
        
        # Create a page for testing
        page = await context.new_page()
        await page.goto('https://www.facebook.com/', timeout=30000)
        print("✅ Initial page created and navigated")
        
        # Test the page scrolling method directly
        comments = await scraper._scrape_comments_with_page_scroll(
            context, test_post_url, max_results=10, existing_page=page
        )
        
        print(f"\n📊 Page Scrolling Results:")
        print(f"   Comments found: {len(comments)}")
        
        if comments:
            print("\n👥 Sample Comments:")
            for i, comment in enumerate(comments[:3]):  # Show first 3
                print(f"   {i+1}. {comment.get('full_name', 'Unknown')} (UID: {comment.get('facebook_uid', 'N/A')})")
                print(f"      Content: {comment.get('interaction_content', 'No content')[:50]}...")
                print(f"      Profile: {comment.get('profile_url', 'No URL')}")
                print()
        else:
            print("⚠️  No comments extracted")
        
        # Test XPath selectors validation
        print("\n🔍 Testing XPath selectors on current page...")
        
        try:
            # Test comments container
            container_count = await page.locator(f"xpath={scraper.page_selectors['comments_container']}").count()
            print(f"   Comments container found: {container_count > 0} (count: {container_count})")
            
            # Test comment items pattern
            items_count = await page.locator(f"xpath={scraper.page_selectors['comment_items_pattern']}").count()
            print(f"   Comment items found: {items_count > 0} (count: {items_count})")
            
            if items_count > 0:
                print("✅ XPath selectors are working on current page")
            else:
                print("⚠️  XPath selectors may need adjustment for current page structure")
                
        except Exception as e:
            print(f"❌ Error testing XPath selectors: {e}")
        
        # Test scrolling behavior
        print("\n📜 Testing scrolling behavior...")
        
        try:
            initial_height = await page.evaluate("document.body.scrollHeight")
            initial_scroll = await page.evaluate("window.pageYOffset")
            
            print(f"   Initial page height: {initial_height}px")
            print(f"   Initial scroll position: {initial_scroll}px")
            
            # Test scroll function
            await scraper._scroll_page_for_comments(page)
            
            final_height = await page.evaluate("document.body.scrollHeight")
            final_scroll = await page.evaluate("window.pageYOffset")
            
            print(f"   Final page height: {final_height}px")
            print(f"   Final scroll position: {final_scroll}px")
            
            if final_scroll > initial_scroll:
                print("✅ Page scrolling is working correctly")
            else:
                print("⚠️  Page scrolling may not be working as expected")
                
        except Exception as e:
            print(f"❌ Error testing scrolling: {e}")
        
        # Cleanup
        print("\n🧹 Cleaning up...")
        await page.close()
        await camoufox_manager.close_browser(profile_id)
        print("✅ Cleanup completed")
        
        return True
        
    except Exception as e:
        print(f"❌ Page scrolling test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_xpath_validation():
    """Test new XPath selectors validation"""
    print("\n🔍 Testing New XPath Selectors Validation")
    print("=" * 60)
    
    try:
        from app.services.facebook_scraper import FacebookScraper
        
        scraper = FacebookScraper()
        
        print("📋 Configured Page XPath Selectors:")
        print()
        
        selectors = scraper.page_selectors
        
        for name, xpath in selectors.items():
            print(f"🎯 {name}:")
            if isinstance(xpath, str) and len(xpath) > 100:
                print(f"   XPath: {xpath[:100]}...")
            else:
                print(f"   XPath: {xpath}")
            
            # Basic XPath validation
            if isinstance(xpath, str):
                if xpath.startswith('//') or xpath.startswith('/html'):
                    print("   ✅ Valid XPath format")
                elif xpath == 'document.body.scrollHeight':
                    print("   ✅ Valid JavaScript expression for scrolling")
                else:
                    print("   ⚠️  Unusual XPath format")
                
                if 'user' in xpath and ('href' in xpath or 'contains' in xpath):
                    print("   ✅ Contains user link pattern")
                elif name in ['user_links', 'avatar_links']:
                    print("   ⚠️  User link selector may need review")
            
            print()
        
        print("✅ XPath validation completed")
        return True
        
    except Exception as e:
        print(f"❌ XPath validation failed: {e}")
        return False

async def main():
    """Main test function"""
    print("🦊 Facebook Page Scrolling Test Suite")
    print("=" * 60)
    print("Testing new features:")
    print("1. Full page scrolling instead of modal scrolling")
    print("2. Updated XPath selectors for page structure")
    print("3. UID extraction from new URL pattern")
    print()
    
    # Test 1: XPath validation
    xpath_success = await test_xpath_validation()
    
    # Test 2: Page scrolling
    page_success = await test_page_scrolling()
    
    print("\n" + "=" * 60)
    print("📊 Test Results:")
    print(f"   XPath Validation: {'✅ PASS' if xpath_success else '❌ FAIL'}")
    print(f"   Page Scrolling: {'✅ PASS' if page_success else '❌ FAIL'}")
    
    if xpath_success and page_success:
        print("\n🎉 ALL TESTS PASSED!")
        print("✅ Page scrolling with new XPaths is ready!")
        print("✅ Full page scrolling implemented successfully!")
        return True
    else:
        print("\n❌ Some tests failed")
        return False

if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        print("\n⏹️  Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Test suite crashed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
