# Database
DATABASE_URL=postgresql+asyncpg://postgres:password@localhost/antidetect_backend

# Redis
REDIS_URL=redis://localhost:6379

# Security
SECRET_KEY=your-secret-key-for-testing-1753369751
PROFILE_ENCRYPTION_KEY=iUeY2Aw9AJCAz1eny2Lg5oMLj-Ubpg-_KY_IeMTMSN0=

# Auto-login service
AUTO_LOGIN_SERVICE_URL=http://localhost:3000

# Performance
ENABLE_CACHING=true
ENABLE_COMPRESSION=true
ENABLE_PERFORMANCE_MONITORING=true

# Testing
TESTING=false
