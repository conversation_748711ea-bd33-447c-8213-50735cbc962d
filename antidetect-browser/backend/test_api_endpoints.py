#!/usr/bin/env python3
"""
Test API endpoints with Camoufox integration
"""

import asyncio
import sys
import json
import aiohttp
from pathlib import Path

BASE_URL = "http://127.0.0.1:8000/api"

async def test_create_profile():
    """Test profile creation via API"""
    print("\n🔧 Testing Profile Creation API...")
    
    try:
        async with aiohttp.ClientSession() as session:
            profile_data = {
                "name": "API Test Profile",
                "proxy_config": {
                    "type": "no_proxy",
                    "host": None,
                    "port": None,
                    "username": None,
                    "password": None
                }
            }
            
            async with session.post(f"{BASE_URL}/profiles/", json=profile_data) as response:
                if response.status == 200:
                    result = await response.json()
                    print("✅ Profile created successfully via API")
                    print(f"   Profile ID: {result.get('id')}")
                    print(f"   Profile name: {result.get('name')}")
                    print(f"   Profile path: {result.get('profile_path')}")
                    return result
                else:
                    error_text = await response.text()
                    print(f"❌ Profile creation failed: {response.status} - {error_text}")
                    return None
                    
    except Exception as e:
        print(f"❌ Profile creation API test failed: {e}")
        return None

async def test_launch_browser(profile_id):
    """Test browser launch via API"""
    print(f"\n🚀 Testing Browser Launch API for profile {profile_id}...")
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(f"{BASE_URL}/profiles/{profile_id}/launch-browser") as response:
                if response.status == 200:
                    result = await response.json()
                    print("✅ Browser launched successfully via API")
                    print(f"   Browser type: {result.get('browser_type', 'unknown')}")
                    print(f"   Antidetect enabled: {result.get('antidetect_enabled', False)}")
                    return True
                else:
                    error_text = await response.text()
                    print(f"❌ Browser launch failed: {response.status} - {error_text}")
                    return False
                    
    except Exception as e:
        print(f"❌ Browser launch API test failed: {e}")
        return False

async def test_browser_status(profile_id):
    """Test browser status via API"""
    print(f"\n🔍 Testing Browser Status API for profile {profile_id}...")
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(f"{BASE_URL}/profiles/{profile_id}/browser-status") as response:
                if response.status == 200:
                    result = await response.json()
                    print("✅ Browser status retrieved successfully via API")
                    print(f"   Browser active: {result.get('browser_active', False)}")
                    print(f"   Browser type: {result.get('browser_type', 'unknown')}")
                    return result.get('browser_active', False)
                else:
                    error_text = await response.text()
                    print(f"❌ Browser status check failed: {response.status} - {error_text}")
                    return False
                    
    except Exception as e:
        print(f"❌ Browser status API test failed: {e}")
        return False

async def test_open_facebook(profile_id):
    """Test Facebook opening via API"""
    print(f"\n🌐 Testing Open Facebook API for profile {profile_id}...")
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(f"{BASE_URL}/profiles/{profile_id}/open-facebook") as response:
                if response.status == 200:
                    result = await response.json()
                    print("✅ Facebook opened successfully via API")
                    print(f"   Browser type: {result.get('browser_type', 'unknown')}")
                    print(f"   Auto-launched: {result.get('auto_launched', False)}")
                    print(f"   Page URL: {result.get('page_url', 'N/A')}")
                    return True
                else:
                    error_text = await response.text()
                    print(f"❌ Facebook opening failed: {response.status} - {error_text}")
                    return False
                    
    except Exception as e:
        print(f"❌ Open Facebook API test failed: {e}")
        return False

async def test_close_browser(profile_id):
    """Test browser close via API"""
    print(f"\n🔒 Testing Browser Close API for profile {profile_id}...")
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(f"{BASE_URL}/profiles/{profile_id}/close-browser") as response:
                if response.status == 200:
                    result = await response.json()
                    print("✅ Browser closed successfully via API")
                    return True
                else:
                    error_text = await response.text()
                    print(f"❌ Browser close failed: {response.status} - {error_text}")
                    return False
                    
    except Exception as e:
        print(f"❌ Browser close API test failed: {e}")
        return False

async def test_get_profiles():
    """Test get all profiles via API"""
    print("\n📋 Testing Get Profiles API...")
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(f"{BASE_URL}/profiles/") as response:
                if response.status == 200:
                    result = await response.json()
                    print("✅ Profiles retrieved successfully via API")
                    print(f"   Number of profiles: {len(result)}")
                    return result
                else:
                    error_text = await response.text()
                    print(f"❌ Get profiles failed: {response.status} - {error_text}")
                    return []
                    
    except Exception as e:
        print(f"❌ Get profiles API test failed: {e}")
        return []

async def main():
    """Main API test function"""
    print("🌐 Camoufox API Integration Test")
    print("=" * 60)
    print("Testing all API endpoints with Camoufox integration")
    print()
    
    # Check if server is running
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(f"{BASE_URL}/profiles/") as response:
                if response.status != 200:
                    print("❌ Server not responding. Please start the server first:")
                    print("   cd backend && source venv/bin/activate && python main.py")
                    return False
    except Exception as e:
        print("❌ Cannot connect to server. Please start the server first:")
        print("   cd backend && source venv/bin/activate && python main.py")
        print(f"   Error: {e}")
        return False
    
    print("✅ Server is running")
    
    # Test workflow
    tests_passed = 0
    total_tests = 6
    
    # 1. Get existing profiles
    profiles = await test_get_profiles()
    if profiles is not None:
        tests_passed += 1
    
    # 2. Create new profile
    profile = await test_create_profile()
    if profile:
        tests_passed += 1
        profile_id = profile.get('id')
        
        # 3. Launch browser
        if await test_launch_browser(profile_id):
            tests_passed += 1
            
            # 4. Check browser status
            if await test_browser_status(profile_id):
                tests_passed += 1
                
                # 5. Open Facebook
                if await test_open_facebook(profile_id):
                    tests_passed += 1
                
                # 6. Close browser
                if await test_close_browser(profile_id):
                    tests_passed += 1
    
    print("\n" + "=" * 60)
    print(f"📊 API Test Results: {tests_passed}/{total_tests} tests passed")
    
    if tests_passed == total_tests:
        print("🎉 ALL API TESTS PASSED!")
        print("✅ Camoufox API integration is working correctly")
        return True
    else:
        print("❌ Some API tests failed")
        return False

if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        print("\n⏹️  Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 API test suite crashed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
