Collecting httpx
  Downloading httpx-0.28.1-py3-none-any.whl (73 kB)
Requirement already satisfied: anyio in ./venv/lib/python3.9/site-packages (from httpx) (4.10.0)
Requirement already satisfied: idna in ./venv/lib/python3.9/site-packages (from httpx) (3.10)
Collecting httpcore==1.*
  Using cached httpcore-1.0.9-py3-none-any.whl (78 kB)
Requirement already satisfied: certifi in ./venv/lib/python3.9/site-packages (from httpx) (2025.8.3)
Requirement already satisfied: h11>=0.16 in ./venv/lib/python3.9/site-packages (from httpcore==1.*->httpx) (0.16.0)
Requirement already satisfied: exceptiongroup>=1.0.2 in ./venv/lib/python3.9/site-packages (from anyio->httpx) (1.3.0)
Requirement already satisfied: sniffio>=1.1 in ./venv/lib/python3.9/site-packages (from anyio->httpx) (1.3.1)
Requirement already satisfied: typing_extensions>=4.5 in ./venv/lib/python3.9/site-packages (from anyio->httpx) (4.14.1)
Installing collected packages: httpcore, httpx
Successfully installed httpcore-1.0.9 httpx-0.28.1
