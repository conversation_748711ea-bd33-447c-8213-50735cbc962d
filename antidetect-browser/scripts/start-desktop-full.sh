#!/bin/bash

# Facebook Automation Desktop - Full Application Startup Script
# This script runs the desktop app with the full main.py backend

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if a command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check if a port is in use
port_in_use() {
    lsof -i :$1 >/dev/null 2>&1
}

# Function to cleanup processes on exit
cleanup() {
    print_status "Cleaning up processes..."

    # Kill auto-login service if running
    if [ ! -z "$AUTOLOGIN_PID" ]; then
        print_status "Stopping auto-login service (PID: $AUTOLOGIN_PID)..."
        kill $AUTOLOGIN_PID 2>/dev/null || true
        wait $AUTOLOGIN_PID 2>/dev/null || true
    fi

    # Kill backend if running
    if [ ! -z "$BACKEND_PID" ]; then
        print_status "Stopping backend server (PID: $BACKEND_PID)..."
        kill $BACKEND_PID 2>/dev/null || true
        wait $BACKEND_PID 2>/dev/null || true
    fi

    # Kill any remaining processes
    pkill -f "uvicorn.*main:app" 2>/dev/null || true
    pkill -f "nest start" 2>/dev/null || true
    pkill -f "electron.*" 2>/dev/null || true

    print_status "Cleanup completed"
}

# Set up signal handlers
trap cleanup EXIT INT TERM

# Get the directory of this script
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

print_status "Starting Facebook Automation Desktop Application (Full Version)"
print_status "Project root: $PROJECT_ROOT"

# Change to project root
cd "$PROJECT_ROOT"

# Check prerequisites
print_status "Checking prerequisites..."

if ! command_exists python3; then
    print_error "Python 3 is required but not installed"
    exit 1
fi

if ! command_exists npm; then
    print_error "Node.js and npm are required but not installed"
    exit 1
fi

# Check if Docker services are running (optional for full version)
if command_exists docker && docker ps >/dev/null 2>&1; then
    if docker ps --format "table {{.Names}}" | grep -q "postgres\|redis"; then
        print_success "Docker services detected and running"
        DOCKER_SERVICES=true
    else
        print_warning "Docker is running but no postgres/redis containers found"
        print_warning "Will use SQLite and local Redis if available"
        DOCKER_SERVICES=false
    fi
else
    print_warning "Docker not available, using local services"
    DOCKER_SERVICES=false
fi

# Setup backend
print_status "Setting up backend environment..."
cd backend

# Create virtual environment if it doesn't exist
if [ ! -d "venv" ]; then
    print_status "Creating Python virtual environment..."
    python3 -m venv venv
fi

# Activate virtual environment
source venv/bin/activate

# Install dependencies
print_status "Installing backend dependencies..."
if [ -f "requirements-minimal.txt" ]; then
    pip install -r requirements-minimal.txt
else
    pip install -r requirements.txt
fi

# Create .env file if it doesn't exist
if [ ! -f ".env" ]; then
    print_status "Creating backend .env file..."
    cat > .env << EOF
# Database Configuration
DATABASE_URL=sqlite+aiosqlite:///./facebook_automation.db

# Redis Configuration (optional)
REDIS_URL=redis://localhost:6379/0

# API Configuration
HOST=127.0.0.1
PORT=8000
DEBUG=true

# Security
SECRET_KEY=desktop-dev-key-$(openssl rand -hex 16 2>/dev/null || echo "fallback-key")
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Application Settings
MAX_CONCURRENT_BROWSERS=3
BROWSER_TIMEOUT=30000
EOF
    print_success "Created .env file with SQLite configuration"
fi

# Initialize database
print_status "Initializing database..."
if [ -f "init_db.py" ]; then
    python init_db.py || print_warning "Database initialization had warnings (this is normal for first run)"
else
    print_warning "init_db.py not found, database will be initialized on first request"
fi

# Create necessary directories
mkdir -p data/profiles data/exports data/logs

# Start auto-login service first
print_status "Starting auto-login service..."
cd ../../auto-login

# Install auto-login dependencies if needed
if [ ! -d "node_modules" ]; then
    print_status "Installing auto-login dependencies..."
    npm install
fi

# Start auto-login service in background
npm run start:dev &
AUTOLOGIN_PID=$!

# Wait for auto-login service to start
print_status "Waiting for auto-login service to start..."
for i in {1..30}; do
    if port_in_use 3000; then
        print_success "Auto-login service started successfully"
        break
    fi
    if [ $i -eq 30 ]; then
        print_error "Auto-login service failed to start within 30 seconds"
        exit 1
    fi
    sleep 1
done

# Create test user for desktop development
print_status "Creating test user for desktop development..."
curl -s -X POST http://localhost:3000/register \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"desktop123"}' \
  >/dev/null 2>&1 || print_warning "Test user may already exist (this is normal)"

print_success "Test user credentials: <EMAIL> / desktop123"

# Go back to backend directory
cd ../antidetect-browser/backend

# Start backend server
print_status "Starting backend server with authentication..."
python main.py &
BACKEND_PID=$!

# Wait for backend to start
print_status "Waiting for backend to start..."
for i in {1..30}; do
    if port_in_use 8000; then
        print_success "Backend server started successfully"
        break
    fi
    if [ $i -eq 30 ]; then
        print_error "Backend server failed to start within 30 seconds"
        exit 1
    fi
    sleep 1
done

# Test backend health
if curl -s http://127.0.0.1:8000/health >/dev/null; then
    print_success "Backend health check passed"
else
    print_warning "Backend health check failed, but continuing..."
fi

# Setup frontend
print_status "Setting up frontend..."
cd ../frontend

# Install frontend dependencies
if [ ! -d "node_modules" ]; then
    print_status "Installing frontend dependencies..."
    npm install
fi

# Build frontend for Electron
print_status "Building frontend for Electron..."
npm run webpack:build

# Start Electron app
print_status "Starting Electron desktop application..."
print_status "Backend logs: backend/logs/backend.log"
print_status ""
print_warning "Press Ctrl+C to stop all services"
print_status "=== Starting Desktop Application ==="

# Start Electron and wait for it to finish
npm start

# The script will continue here when Electron exits
print_status "Desktop application closed"
