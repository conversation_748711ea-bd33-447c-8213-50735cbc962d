[{"property": "navigator.userAgent", "type": "str"}, {"property": "navigator.doNotTrack", "type": "str"}, {"property": "navigator.appCodeName", "type": "str"}, {"property": "navigator.appName", "type": "str"}, {"property": "navigator.appVersion", "type": "str"}, {"property": "navigator.oscpu", "type": "str"}, {"property": "navigator.language", "type": "str"}, {"property": "navigator.languages", "type": "array"}, {"property": "navigator.platform", "type": "str"}, {"property": "navigator.hardwareConcurrency", "type": "uint"}, {"property": "navigator.product", "type": "str"}, {"property": "navigator.productSub", "type": "str"}, {"property": "navigator.maxTouchPoints", "type": "uint"}, {"property": "navigator.<PERSON><PERSON><PERSON>bled", "type": "bool"}, {"property": "navigator.globalPrivacyControl", "type": "bool"}, {"property": "navigator.buildID", "type": "str"}, {"property": "navigator.onLine", "type": "bool"}, {"property": "screen.availHeight", "type": "uint"}, {"property": "screen.<PERSON><PERSON><PERSON><PERSON>", "type": "uint"}, {"property": "screen.availTop", "type": "uint"}, {"property": "screen.availLeft", "type": "uint"}, {"property": "screen.height", "type": "uint"}, {"property": "screen.width", "type": "uint"}, {"property": "screen.colorDepth", "type": "uint"}, {"property": "screen.pixelDepth", "type": "uint"}, {"property": "screen.pageXOffset", "type": "double"}, {"property": "screen.pageYOffset", "type": "double"}, {"property": "window.scrollMinX", "type": "int"}, {"property": "window.scrollMinY", "type": "int"}, {"property": "window.scrollMaxX", "type": "int"}, {"property": "window.scrollMaxY", "type": "int"}, {"property": "window.outerHeight", "type": "uint"}, {"property": "window.outerWidth", "type": "uint"}, {"property": "window.innerHeight", "type": "uint"}, {"property": "window.innerWidth", "type": "uint"}, {"property": "window.screenX", "type": "int"}, {"property": "window.screenY", "type": "int"}, {"property": "window.history.length", "type": "uint"}, {"property": "window.devicePixelRatio", "type": "double"}, {"property": "document.body.clientWidth", "type": "uint"}, {"property": "document.body.clientHeight", "type": "uint"}, {"property": "document.body.clientTop", "type": "uint"}, {"property": "document.body.clientLeft", "type": "uint"}, {"property": "headers.User-Agent", "type": "str"}, {"property": "headers.Accept-Language", "type": "str"}, {"property": "headers.Accept-Encoding", "type": "str"}, {"property": "webrtc:ipv4", "type": "str"}, {"property": "webrtc:ipv6", "type": "str"}, {"property": "webrtc:localipv4", "type": "str"}, {"property": "webrtc:localipv6", "type": "str"}, {"property": "pdfViewerEnabled", "type": "bool"}, {"property": "battery:charging", "type": "bool"}, {"property": "battery:chargingTime", "type": "double"}, {"property": "battery:dischargingTime", "type": "double"}, {"property": "battery:level", "type": "double"}, {"property": "fonts", "type": "array"}, {"property": "fonts:spacing_seed", "type": "uint"}, {"property": "geolocation:latitude", "type": "double"}, {"property": "geolocation:longitude", "type": "double"}, {"property": "geolocation:accuracy", "type": "double"}, {"property": "timezone", "type": "str"}, {"property": "locale:language", "type": "str"}, {"property": "locale:region", "type": "str"}, {"property": "locale:script", "type": "str"}, {"property": "locale:all", "type": "str"}, {"property": "humanize", "type": "bool"}, {"property": "humanize:maxTime", "type": "double"}, {"property": "humanize:minTime", "type": "double"}, {"property": "showcursor", "type": "bool"}, {"property": "AudioContext:sampleRate", "type": "uint"}, {"property": "AudioContext:outputLatency", "type": "double"}, {"property": "AudioContext:maxChannelCount", "type": "uint"}, {"property": "webGl:renderer", "type": "str"}, {"property": "webGl:vendor", "type": "str"}, {"property": "webGl:supportedExtensions", "type": "array"}, {"property": "webGl2:supportedExtensions", "type": "array"}, {"property": "webGl:parameters", "type": "dict"}, {"property": "webGl:parameters:blockIfNotDefined", "type": "bool"}, {"property": "webGl2:parameters", "type": "dict"}, {"property": "webGl2:parameters:blockIfNotDefined", "type": "bool"}, {"property": "webGl:shaderPrecisionFormats", "type": "dict"}, {"property": "webGl:shaderPrecisionFormats:blockIfNotDefined", "type": "bool"}, {"property": "webGl2:shaderPrecisionFormats", "type": "dict"}, {"property": "webGl2:shaderPrecisionFormats:blockIfNotDefined", "type": "bool"}, {"property": "webGl:contextAttributes", "type": "dict"}, {"property": "webGl2:contextAttributes", "type": "dict"}, {"property": "canvas:a<PERSON><PERSON><PERSON><PERSON>", "type": "int"}, {"property": "canvas:aaCapOffset", "type": "bool"}, {"property": "voices", "type": "array"}, {"property": "voices:blockIfNotDefined", "type": "bool"}, {"property": "voices:fakeCompletion", "type": "bool"}, {"property": "voices:fakeCompletion:ch<PERSON><PERSON><PERSON><PERSON>econd", "type": "double"}, {"property": "mediaDevices:micros", "type": "uint"}, {"property": "mediaDevices:webcams", "type": "uint"}, {"property": "mediaDevices:speakers", "type": "uint"}, {"property": "mediaDevices:enabled", "type": "bool"}, {"property": "allowMainWorld", "type": "bool"}, {"property": "forceScopeAccess", "type": "bool"}, {"property": "enableRemoteSubframes", "type": "bool"}, {"property": "disableTheming", "type": "bool"}, {"property": "memorysaver", "type": "bool"}, {"property": "addons", "type": "array"}, {"property": "certificatePaths", "type": "array"}, {"property": "certificates", "type": "array"}, {"property": "debug", "type": "bool"}]