{"__COMMENT__ More Information": "https://github.com/mozilla/policy-templates/blob/master/README.md", "policies": {"AppUpdateURL": "https://localhost", "DisableAppUpdate": true, "OverrideFirstRunPage": "", "OverridePostUpdatePage": "", "DisableSystemAddonUpdate": true, "DisableProfileImport": true, "DisableFirefoxStudies": true, "DisableTelemetry": true, "DisableFeedbackCommands": true, "DisablePocket": true, "DisableSetDesktopBackground": true, "DisableDeveloperTools": false, "NoDefaultBookmarks": true, "DisableFirefoxScreenshots": true, "DisableSafeMode": true, "DisplayBookmarksToolbar": "never", "DontCheckDefaultBrowser": true, "DisableFirefoxAccounts": true, "DisableFormHistory": true, "HardwareAcceleration": false, "WindowsSSO": false, "PasswordManagerEnabled": false, "OfferToSaveLogins": false, "ExtensionUpdate": false, "PDFjs": {"Enabled": true, "EnablePermissions": true}, "WebsiteFilter": {"Block": ["https://localhost/*"], "Exceptions": ["https://localhost/*"]}, "Extensions": {"Uninstall": ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"]}, "ExtensionSettings": {"<EMAIL>": {"default_area": "navbar", "updates_disabled": true}, "magnolia@12.34": {"default_area": "navbar", "updates_disabled": true}}, "SearchEngines": {"PreventInstalls": true, "Remove": ["Google", "DuckDuckGo", "<PERSON>", "Amazon.com", "eBay", "Twitter", "Wikipedia (en)"], "Default": "None", "Add": [{"Name": "None", "Description": "None", "Alias": "", "Method": "POST", "URLTemplate": "http://127.0.0.1", "PostData": ""}]}}}