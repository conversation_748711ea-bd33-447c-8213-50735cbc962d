{"navigator.userAgent$__UA": "str", "navigator.appVersion$__UA": "str", "navigator.platform$__UA": "str", "navigator.oscpu$__UA": "str", "navigator.appCodeName$__PROD_CODE": "str", "navigator.appName$__PROD_CODE": "str", "navigator.product$__PROD_CODE": "str", "navigator.productSub": "str[/^\\d+$/]", "navigator.buildID": "str[/^\\d+$/]", "screen.height$__SC": "int[>0]", "screen.width$__SC": "int[>0]", "screen.availHeight$__SC": "int[>=0]", "screen.availWidth$__SC": "int[>=0]", "screen.availTop": "int[>=0]", "screen.availLeft": "int[>=0]", "locale:language$__LOCALE": "str", "locale:region$__LOCALE": "str", "locale:script": "str", "geolocation:latitude$__GEO": "double[-90 - 90]", "geolocation:longitude$__GEO": "double[-180 - 180]", "geolocation:accuracy": "double[>=0]", "timezone": "str[/^[\\w_]+/[\\w_]+$/]", "locale:all": "str", "headers.Accept-Language": "str", "navigator.language": "str", "navigator.languages": "array[str]", "headers.User-Agent": "str", "headers.Accept-Encoding": "str", "navigator.doNotTrack": "str[0, 1, unspecified]", "navigator.hardwareConcurrency": "int[>0]", "navigator.maxTouchPoints": "int[>=0]", "navigator.cookieEnabled": "bool", "navigator.globalPrivacyControl": "bool", "navigator.onLine": "bool", "window.history.length": "int[>=0]", "pdfViewerEnabled": "bool", "window.outerHeight$__W_OUTER": "int[>0]", "window.outerWidth$__W_OUTER": "int[>0]", "window.innerHeight$__W_INNER": "int[>0]", "window.innerWidth$__W_INNER": "int[>0]", "screen.colorDepth": "int[>0]", "screen.pixelDepth": "int[>0]", "screen.pageXOffset": "double", "screen.pageYOffset": "double", "window.scrollMinX": "int", "window.scrollMinY": "int", "window.scrollMaxX": "int", "window.scrollMaxY": "int", "window.screenX": "int", "window.screenY": "int", "window.devicePixelRatio": "double[>0]", "document.body.clientWidth$__DOC_BODY": "int[>=0]", "document.body.clientHeight$__DOC_BODY": "int[>=0]", "document.body.clientTop": "int", "document.body.clientLeft": "int", "webrtc:ipv4": "@IPV4", "webrtc:ipv6": "@IPV6", "webrtc:localipv4": "@IPV4", "webrtc:localipv6": "@IPV6", "@IPV4": "str[/^(?:[0-9]{1,3}\\.){3}[0-9]{1,3}$/]", "@IPV6": "str[/^(([0-9a-fA-F]{0,4}:){1,7}[0-9a-fA-F]{0,4})$/]", "battery:charging$__BATTERY": "bool", "battery:chargingTime$__BATTERY": "double[>=0]", "battery:dischargingTime$__BATTERY": "double[>=0]", "battery:level$__BATTERY": "double[>0]", "fonts": "array[str]", "fonts:spacing_seed": "int[>=0]", "AudioContext:sampleRate": "int[>=0]", "AudioContext:outputLatency": "double[>=0]", "AudioContext:maxChannelCount": "int[>=0]", "mediaDevices:micros": "int[>=0]", "mediaDevices:webcams": "int[>=0]", "mediaDevices:speakers": "int[>=0]", "mediaDevices:enabled": "bool", "webGl:renderer$__WEBGL": "str", "webGl:vendor$__WEBGL": "str", "webGl:supportedExtensions": "array[str[/^[\\w_]+$/]]", "webGl2:supportedExtensions": "array[str[/^[\\w_]+$/]]", "webGl:parameters": "@WEBGL_PARAMS", "webGl2:parameters": "@WEBGL_PARAMS", "webGl:parameters:blockIfNotDefined": "bool", "webGl2:parameters:blockIfNotDefined": "bool", "webGl:shaderPrecisionFormats": "@WEBGL_SHADER_PRECISION_FORMATS", "webGl2:shaderPrecisionFormats": "@WEBGL_SHADER_PRECISION_FORMATS", "webGl:shaderPrecisionFormats:blockIfNotDefined": "bool", "webGl2:shaderPrecisionFormats:blockIfNotDefined": "bool", "webGl:contextAttributes": "@WEBGL_CONTEXT_ATTRIBUTES", "webGl2:contextAttributes": "@WEBGL_CONTEXT_ATTRIBUTES", "@WEBGL_PARAMS": {"2849": "int", "2884": "bool", "2885": "int", "2886": "int", "2928": "array[int, 2]", "2929": "bool", "2930": "bool", "2931": "int", "2932": "int", "2960": "bool", "2961": "int", "2962": "int", "2963": "int", "2964": "int", "2965": "int", "2966": "int", "2967": "int", "2968": "int", "2978": "array[int, 4]", "3024": "bool", "3042": "bool", "3074": "int | nil", "3088": "array[int, 4]", "3089": "bool", "3106": "array[int, 4]", "3107": "array[bool, 4]", "3314": "int | nil", "3315": "int | nil", "3316": "int | nil", "3317": "int", "3330": "int | nil", "3331": "int | nil", "3332": "int | nil", "3333": "int", "3379": "int", "3386": "array[int, 2]", "3408": "int", "3410": "int", "3411": "int", "3412": "int", "3413": "int", "3414": "int", "3415": "int", "7936": "str", "7937": "str", "7938": "str", "10752": "int", "32773": "array[int, 4]", "32777": "int", "32823": "bool", "32824": "int", "32873": "nil", "32877": "int | nil", "32878": "int | nil", "32883": "int | nil", "32926": "bool", "32928": "bool", "32936": "int", "32937": "int", "32938": "int", "32939": "bool", "32968": "int", "32969": "int", "32970": "int", "32971": "int", "33000": "int | nil", "33001": "int | nil", "33170": "int", "33901": "array[double, 2]", "33902": "array[double, 2]", "34016": "int", "34024": "int", "34045": "int | nil", "34047": "nil", "34068": "nil", "34076": "int", "34467": "nil", "34816": "int", "34817": "int", "34818": "int", "34819": "int", "34852": "int | nil", "34853": "int | nil", "34854": "int | nil", "34855": "int | nil", "34856": "int | nil", "34857": "int | nil", "34858": "int | nil", "34859": "int | nil", "34860": "int | nil", "34877": "int", "34921": "int", "34930": "int", "34964": "nil", "34965": "nil", "35071": "int | nil", "35076": "int | nil", "35077": "int | nil", "35371": "int | nil", "35373": "int | nil", "35374": "int | nil", "35375": "int | nil", "35376": "int | nil", "35377": "int | nil", "35379": "int | nil", "35380": "int | nil", "35657": "int | nil", "35658": "int | nil", "35659": "int | nil", "35660": "int", "35661": "int", "35723": "int | nil", "35724": "str", "35725": "nil", "35738": "int", "35739": "int", "35968": "int | nil", "35977": "bool | nil", "35978": "int | nil", "35979": "int | nil", "36003": "int", "36004": "int", "36005": "int", "36006": "nil", "36007": "nil", "36063": "int | nil", "36183": "int | nil", "36203": "int | nil", "36345": "int | nil", "36347": "int", "36348": "int", "36349": "int", "36387": "bool | nil", "36388": "bool | nil", "36392": "nil", "36795": "nil", "37137": "int | double | nil", "37154": "int | nil", "37157": "int | nil", "37440": "bool", "37441": "bool", "37443": "int", "37444": "nil", "37445": "str", "37446": "str", "37447": "int | nil", "38449": "nil"}, "@WEBGL_SHADER_PRECISION_FORMATS": {"/^\\d+,\\d+$/": {"*rangeMin": "int[>=0]", "*rangeMax": "int[>=0]", "*precision": "int[>=0]"}}, "@WEBGL_CONTEXT_ATTRIBUTES": {"alpha": "bool", "antialias": "bool", "depth": "bool", "failIfMajorPerformanceCaveat": "bool", "powerPreference": "str[low, high, default]", "premultipliedAlpha": "bool", "preserveDrawingBuffer": "bool", "stencil": "bool"}, "canvas:aaOffset": "int", "canvas:aaCapOffset": "bool", "voices": "array[@VOICE_TYPE]", "voices:blockIfNotDefined": "bool", "voices:fakeCompletion": "bool", "voices:fakeCompletion:charsPerSecond": "double[>0]", "@VOICE_TYPE": {"*isLocalService": "bool", "*isDefault": "bool", "*voiceURI": "str", "*name": "str", "*lang": "str"}, "humanize": "bool", "humanize:maxTime": "double[>=0]", "humanize:minTime": "double[>=0]", "showcursor": "bool", "allowMainWorld": "bool", "forceScopeAccess": "bool", "enableRemoteSubframes": "bool", "disableTheming": "bool", "memorysaver": "bool", "addons": "array[str]", "certificatePaths": "array[str]", "certificates": "array[str]", "debug": "bool"}