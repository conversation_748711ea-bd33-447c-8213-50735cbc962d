---
name: Camoufox detected
about: Report a website that detects Camoufox
title: ''
labels: detection-issue
assignees: ''

---

### Website detecting Camoufox:

What website or WAF is flagging Camoufox? Provide as much detail and additional context as possible.

### Screenshots:

If applicable, add screenshots to help explain your problem.

### To Reproduce:

Send a testing site that reproduces the issue, and a snippet of your script. Provide your `Camoufox()` or `AsyncCamoufox()` initialization.

### Other questions:

These questions will help me diagnose the issue:

1. Are you using a proxy?



2. Open the website in a private tab in your personal browser using the same IP. Does it work?



3. Is Camoufox detected randomly or every time?



4. What OS are you using?



### Version:

Run `python -m camoufox version` in your terminal and paste the output here.