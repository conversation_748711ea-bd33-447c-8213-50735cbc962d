name: Build and Release

on:
  workflow_dispatch:
  push:
    tags:
      - "*"

jobs:
  build:
    runs-on: ubuntu-24.04
    strategy:
      matrix:
        target: [linux, windows, macos]
        arch: [x86_64, arm64, i686]
        exclude:
          # Fails (.mozbuild does not include clang++-cl)
          - target: windows
            arch: arm64
          # Unsuported
          - target: macos
            arch: i686

    steps:
      - name: Maximize build space
        uses: AdityaGarg8/remove-unwanted-software@v4.1
        with:
          remove-dotnet: "true"
          remove-android: "true"
          remove-haskell: "true"
          remove-codeql: "true"
          remove-docker-images: "true"
          remove-cached-tools: "true"
          remove-swapfile: "true"
          verbose: "false"

      - name: Remove unwanted tools
        # Originally from here: https://github.com/AdityaGarg8/remove-unwanted-software/blob/master/action.yml
        run: |
          sudo apt-get remove -y '^aspnetcore-.*' > /dev/null
          sudo apt-get remove -y '^dotnet-.*' > /dev/null
          sudo apt-get remove -y '^llvm-.*' > /dev/null
          sudo apt-get remove -y 'php.*' > /dev/null
          sudo apt-get remove -y '^mongodb-.*' > /dev/null
          sudo apt-get remove -y '^mysql-.*' > /dev/null
          sudo apt-get remove -y azure-cli google-chrome-stable firefox ${POWERSHELL} mono-devel libgl1-mesa-dri --fix-missing > /dev/null
          if [[ (${CODENAME} = focal) || (${CODENAME} = jammy) ]]; then
            sudo apt-get remove -y google-cloud-sdk --fix-missing > /dev/null
            sudo apt-get remove -y google-cloud-cli --fix-missing > /dev/null
          fi
          sudo apt-get autoremove -y > /dev/null
          sudo apt-get clean > /dev/null

      - uses: actions/checkout@v2
        with:
          fetch-depth: 1

      - name: Set up Go
        uses: actions/setup-go@v2
        with:
          go-version: "1.23"

      - name: Set up Python
        uses: actions/setup-python@v2
        with:
          python-version: "3.11"

      - name: Set up LLVM
        run: |
          wget https://apt.llvm.org/llvm.sh
          chmod +x llvm.sh
          sudo ./llvm.sh 18
          sudo apt-get install -y lld-18 clang-18
          if [ "${{ matrix.arch }}" != "x86_64" ]; then
            sudo apt-get install -y libc6-i386 lib32gcc-s1 lib32stdc++6 gcc-multilib g++-multilib
          fi
          sudo update-alternatives --install /usr/bin/ld.lld ld.lld /usr/bin/ld.lld-18 100

      - name: Check disk space
        run: df -h

      - name: Install dependencies
        run: |
          sudo apt-get update
          sudo apt-get install -y msitools p7zip-full aria2

      - name: Fetch source
        env:
          CAMOUFOX_PASSWD: ${{ secrets.CAMOUFOX_PASSWD }}
        run: |
          make fetch

      - name: Setup and bootstrap
        run: |
          make setup-minimal
          make mozbootstrap
          mkdir -p dist

      - name: Create swap space
        run: |
          sudo fallocate -l 16G /swapfile
          sudo chmod 600 /swapfile
          sudo mkswap /swapfile
          sudo swapon /swapfile
          free -h

      - name: Build
        run: python3 ./multibuild.py --target ${{ matrix.target }} --arch ${{ matrix.arch }}

      - name: Upload artifacts
        uses: actions/upload-artifact@v4
        with:
          name: CamoufoxBuilds-${{ matrix.target }}-${{ matrix.arch }}
          path: dist/*

  release:
    needs: build
    permissions:
      contents: write
    runs-on: ubuntu-latest

    steps:
      - name: Download all artifacts
        uses: actions/download-artifact@v4
        with:
          path: artifacts

      - name: Create Release
        uses: softprops/action-gh-release@v1
        if: startsWith(github.ref, 'refs/tags/')
        with:
          files: artifacts/**/*
          generate_release_notes: true
          draft: true
          prerelease: false
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
