--- a/browser/components/asrouter/content/asrouter-admin.bundle.js
+++ b/browser/components/asrouter/content/asrouter-admin.bundle.js
@@ -1641,7 +1641,7 @@ class ASRouterAdminInner extends (react__WEBPACK_IMPORTED_MODULE_1___default().P
         label = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default().createElement("span", null, "remote settings (", /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default().createElement("a", {
           className: "providerUrl",
           target: "_blank",
-          href: "https://firefox.settings.services.mozilla.com/v1/buckets/main/collections/nimbus-desktop-experiments/records",
+          href: "https://see.ticket.about.this.invalid",
           rel: "noopener noreferrer"
         }, "nimbus-desktop-experiments"), ")");
       }
--- a/services/settings/Utils.sys.mjs
+++ b/services/settings/Utils.sys.mjs
@@ -51,6 +51,9 @@ ChromeUtils.defineLazyGetter(lazy, "isRunningTests", () => {
 // Overriding the server URL is normally disabled on Beta and Release channels,
 // except under some conditions.
 ChromeUtils.defineLazyGetter(lazy, "allowServerURLOverride", () => {
+
+  return true; // always override in LW
+
   if (!AppConstants.RELEASE_OR_BETA) {
     // Always allow to override the server URL on Nightly/DevEdition.
     return true;
--- a/toolkit/components/search/SearchUtils.sys.mjs
+++ b/toolkit/components/search/SearchUtils.sys.mjs
@@ -193,9 +193,9 @@ export var SearchUtils = {
 
   ENGINES_URLS: {
     "prod-main":
-      "https://firefox.settings.services.mozilla.com/v1/buckets/main/collections/search-config/records",
+      "https://%.invalid",
     "prod-preview":
-      "https://firefox.settings.services.mozilla.com/v1/buckets/main-preview/collections/search-config/records",
+      "https://%.invalid",
     "stage-main":
       "https://firefox.settings.services.allizom.org/v1/buckets/main/collections/search-config/records",
     "stage-preview":
