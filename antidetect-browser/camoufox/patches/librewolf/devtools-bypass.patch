diff --git a/devtools/server/actors/thread.js b/devtools/server/actors/thread.js
index d91f463..3c1ba5a 100644
--- a/devtools/server/actors/thread.js
+++ b/devtools/server/actors/thread.js
@@ -386,7 +386,8 @@ class ThreadActor extends Actor {
   attach(options) {
     // Note that the client avoids trying to call attach if already attached.
     // But just in case, avoid any possible duplicate call to attach.
-    if (this.alreadyAttached) {
+    let forceDetach = Services.prefs.getBoolPref("camoufox.debugger.force_detach", false);
+    if (this.alreadyAttached || forceDetach) {
       return;
     }
 
diff --git a/devtools/server/actors/webconsole/listeners/console-api.js b/devtools/server/actors/webconsole/listeners/console-api.js
index 3e5d0bc..8260e2b 100644
--- a/devtools/server/actors/webconsole/listeners/console-api.js
+++ b/devtools/server/actors/webconsole/listeners/console-api.js
@@ -97,7 +97,8 @@ class ConsoleAPIListener {
    *        The message object receives from the ConsoleAPIStorage service.
    */
   onConsoleAPILogEvent(message) {
-    if (!this.handler) {
+    let disableConsole = Services.prefs.getBoolPref("camoufox.console.logging_disabled", false);
+    if (!this.handler || disableConsole) {
       return;
     }
