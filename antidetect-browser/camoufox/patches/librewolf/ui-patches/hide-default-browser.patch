diff --git a/browser/components/preferences/main.inc.xhtml b/browser/components/preferences/main.inc.xhtml
index 8485423..b007b96 100644
--- a/browser/components/preferences/main.inc.xhtml
+++ b/browser/components/preferences/main.inc.xhtml
@@ -29,6 +29,8 @@
   <vbox id="startupPageBox">
     <checkbox id="browserRestoreSession"
               data-l10n-id="startup-restore-windows-and-tabs"/>
+    <html:a is="moz-support-link" support-page="session-restore" data-l10n-id="session-restore-learn-more" />
+
 #ifdef XP_WIN
     <hbox id="windowsLaunchOnLoginBox" align="center" hidden="true">
       <checkbox id="windowsLaunchOnLogin"
@@ -46,7 +48,7 @@
   </vbox>
 
 #ifdef HAVE_SHELL_SERVICE
-  <vbox id="defaultBrowserBox">
+  <vbox id="defaultBrowserBox" hidden="true">
     <checkbox id="alwaysCheckDefault" preference="browser.shell.checkDefaultBrowser"
               disabled="true"
               data-l10n-id="always-check-default"/>
