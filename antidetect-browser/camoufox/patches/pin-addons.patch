diff --git a/browser/base/content/browser-addons.js b/browser/base/content/browser-addons.js
index d071b231aa..9bcf162160 100644
--- a/browser/base/content/browser-addons.js
+++ b/browser/base/content/browser-addons.js
@@ -1924,6 +1924,8 @@ var gUnifiedExtensions = {
   // class and not the `.toolbarbutton-1` one. When NOT placed in the panel,
   // it is the other way around.
   _updateWidgetClassName(aWidgetId, inPanel) {
+    inPanel = false;
+    this.pinToToolbar(aWidgetId, true);
     if (!CustomizableUI.isWebExtensionWidget(aWidgetId)) {
       return;
     }
diff --git a/browser/base/content/main-popupset.inc.xhtml b/browser/base/content/main-popupset.inc.xhtml
index 1fb595272a..389d436e90 100644
--- a/browser/base/content/main-popupset.inc.xhtml
+++ b/browser/base/content/main-popupset.inc.xhtml
@@ -286,7 +286,6 @@
               type="checkbox"
               contexttype="toolbaritem"
               class="customize-context-pinToToolbar"/>
-    <menuseparator id="toolbarDownloadsAnchorMenuSeparator"/>
     <menuitem id="toolbar-context-always-open-downloads-panel"
               type="checkbox"
               data-lazy-l10n-id="toolbar-context-menu-always-open-downloads-panel"
