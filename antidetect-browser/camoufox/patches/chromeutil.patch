diff --git a/dom/base/ChromeUtils.cpp b/dom/base/ChromeUtils.cpp
index 52f0af76ec..2a7a9ae4fc 100644
--- a/dom/base/ChromeUtils.cpp
+++ b/dom/base/ChromeUtils.cpp
@@ -5,6 +5,8 @@
  * file, You can obtain one at http://mozilla.org/MPL/2.0/. */
 
 #include "ChromeUtils.h"
+#include "MaskConfig.hpp"
+#include "MouseTrajectories.hpp"
 
 #include "JSOracleParent.h"
 #include "ThirdPartyUtil.h"
@@ -2115,6 +2117,24 @@ bool ChromeUtils::IsDarkBackground(GlobalObject&, Element& aElement) {
   return nsNativeTheme::IsDarkBackground(f);
 }
 
+/* static */
+void ChromeUtils::CamouDebug(GlobalObject& aGlobal, const nsAString& aVarName) {
+  if (auto value = MaskConfig::GetBool("debug");
+      value.has_value() && !value.value()) {
+    return;
+  }
+  NS_ConvertUTF16toUTF8 utf8VarName(aVarName);
+  printf_stderr("DEBUG: %s\n", utf8VarName.get());
+}
+
+bool ChromeUtils::IsCamouDebug(GlobalObject& aGlobal) {
+  if (auto value = MaskConfig::GetBool("debug");
+      value.has_value() && value.value()) {
+    return true;
+  }
+  return false;
+}
+
 double ChromeUtils::DateNow(GlobalObject&) { return JS_Now() / 1000.0; }
 
 /* static */
@@ -2141,6 +2161,77 @@ void ChromeUtils::GetAllPossibleUtilityActorNames(GlobalObject& aGlobal,
   }
 }
 
+/* static */
+int32_t ChromeUtils::CamouGetInt(GlobalObject& aGlobal,
+                                 const nsAString& aVarName) {
+  NS_ConvertUTF16toUTF8 utf8VarName(aVarName);
+  if (auto value = MaskConfig::GetInt32(utf8VarName.get())) {
+    return value.value();
+  }
+  return 0;
+}
+
+/* static */
+double ChromeUtils::CamouGetDouble(GlobalObject& aGlobal,
+                                   const nsAString& aVarName,
+                                   double aDefaultValue) {
+  NS_ConvertUTF16toUTF8 utf8VarName(aVarName);
+  if (auto value = MaskConfig::GetDouble(utf8VarName.get())) {
+    return value.value();
+  }
+  return aDefaultValue;
+}
+
+/* static */
+bool ChromeUtils::CamouGetBool(GlobalObject& aGlobal, const nsAString& aVarName,
+                               bool aDefaultValue) {
+  NS_ConvertUTF16toUTF8 utf8VarName(aVarName);
+  if (auto value = MaskConfig::GetBool(utf8VarName.get())) {
+    return value.value();
+  }
+  return aDefaultValue;
+}
+
+/* static */
+void ChromeUtils::CamouGetString(GlobalObject& aGlobal,
+                                 const nsAString& aVarName,
+                                 nsAString& aRetVal) {
+  NS_ConvertUTF16toUTF8 utf8VarName(aVarName);
+  if (auto value = MaskConfig::GetString(utf8VarName.get())) {
+    aRetVal.Assign(NS_ConvertUTF8toUTF16(value.value()));
+  } else {
+    aRetVal.Truncate();
+  }
+}
+
+/* static */
+void ChromeUtils::CamouGetStringList(GlobalObject& aGlobal,
+                                     const nsAString& aVarName,
+                                     nsTArray<nsString>& aRetVal) {
+  NS_ConvertUTF16toUTF8 utf8VarName(aVarName);
+  if (auto values = MaskConfig::GetStringList(utf8VarName.get());
+      !values.empty()) {
+    aRetVal.Clear();
+    for (const auto& str : values) {
+      aRetVal.AppendElement(NS_ConvertUTF8toUTF16(str));
+    }
+    return;
+  }
+  aRetVal.Clear();
+}
+
+/* static */
+void ChromeUtils::CamouGetMouseTrajectory(GlobalObject& aGlobal, long aFromX,
+                                          long aFromY, long aToX, long aToY,
+                                          nsTArray<int32_t>& aPoints) {
+  HumanizeMouseTrajectory trajectory(std::make_pair(aFromX, aFromY),
+                                     std::make_pair(aToX, aToY));
+  std::vector<int> flattenedPoints = trajectory.getPoints();
+
+  aPoints.Clear();
+  aPoints.AppendElements(flattenedPoints.data(), flattenedPoints.size());
+}
+
 /* static */
 bool ChromeUtils::ShouldResistFingerprinting(
     GlobalObject& aGlobal, JSRFPTarget aTarget,
diff --git a/dom/base/ChromeUtils.h b/dom/base/ChromeUtils.h
index 138b9c3f80..c7c7ce74bf 100644
--- a/dom/base/ChromeUtils.h
+++ b/dom/base/ChromeUtils.h
@@ -305,6 +305,10 @@ class ChromeUtils {
 
   static bool IsDarkBackground(GlobalObject&, Element&);
 
+  static void CamouDebug(GlobalObject& aGlobal, const nsAString& aVarName);
+
+  static bool IsCamouDebug(GlobalObject& aGlobal);
+
   static double DateNow(GlobalObject&);
 
   static void EnsureJSOracleStarted(GlobalObject&);
@@ -314,6 +318,24 @@ class ChromeUtils {
   static void GetAllPossibleUtilityActorNames(GlobalObject& aGlobal,
                                               nsTArray<nsCString>& aNames);
 
+  static int32_t CamouGetInt(GlobalObject& aGlobal, const nsAString& aVarName);
+
+  static double CamouGetDouble(GlobalObject& aGlobal, const nsAString& aVarName,
+                               double aDefaultValue);
+
+  static bool CamouGetBool(GlobalObject& aGlobal, const nsAString& aVarName,
+                            bool aDefaultValue);
+
+  static void CamouGetString(GlobalObject& aGlobal, const nsAString& aVarName,
+                             nsAString& aRetVal);
+
+  static void CamouGetStringList(GlobalObject& aGlobal, const nsAString& aVarName,
+                                 nsTArray<nsString>& aRetVal);
+
+  static void CamouGetMouseTrajectory(GlobalObject& aGlobal, long aFromX,
+                                     long aFromY, long aToX, long aToY,
+                                     nsTArray<int32_t>& aPoints);
+
   static bool ShouldResistFingerprinting(
       GlobalObject& aGlobal, JSRFPTarget aTarget,
       const Nullable<uint64_t>& aOverriddenFingerprintingSettings);
diff --git a/dom/chrome-webidl/ChromeUtils.webidl b/dom/chrome-webidl/ChromeUtils.webidl
index 6a99703db1..82415eba19 100644
--- a/dom/chrome-webidl/ChromeUtils.webidl
+++ b/dom/chrome-webidl/ChromeUtils.webidl
@@ -750,6 +750,13 @@ partial namespace ChromeUtils {
    */
   boolean isDarkBackground(Element element);
 
+  /**
+   * Camoufox debug commands
+   */
+  undefined camouDebug(DOMString varName);
+
+  boolean isCamouDebug();
+
   /**
    * Starts the JSOracle process for ORB JavaScript validation, if it hasn't started already.
    */
@@ -761,6 +768,36 @@ partial namespace ChromeUtils {
   [ChromeOnly]
   readonly attribute unsigned long aliveUtilityProcesses;
 
+  /**
+   * Get an int value from Camoufox MaskConfig.
+   */
+  long camouGetInt(DOMString varName);
+
+  /**
+   * Get a double value from Camoufox MaskConfig.
+   */
+  double camouGetDouble(DOMString varName, double defaultValue);
+
+  /**
+   * Get a bool value from Camoufox MaskConfig.
+   */
+  boolean camouGetBool(DOMString varName, boolean defaultValue);
+
+  /**
+   * Get a string value from Camoufox MaskConfig.
+   */
+  DOMString camouGetString(DOMString varName);
+
+  /**
+   * Get a list of strings from Camoufox MaskConfig.
+   */
+  sequence<DOMString> camouGetStringList(DOMString varName);
+
+  /**
+   * Calculate a human-like mouse trajectory between two points.
+   */
+  sequence<long> camouGetMouseTrajectory(long fromX, long fromY, long toX, long toY);
+
   /**
    * Get a list of all possible Utility process Actor Names ; mostly useful to
    * perform testing and ensure about:processes display is sound and misses no
