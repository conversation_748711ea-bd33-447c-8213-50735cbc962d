diff --git a/dom/geolocation/Geolocation.cpp b/dom/geolocation/Geolocation.cpp
index 274cdebc2e..b0183ecc6a 100644
--- a/dom/geolocation/Geolocation.cpp
+++ b/dom/geolocation/Geolocation.cpp
@@ -34,6 +34,7 @@
 #include "nsServiceManagerUtils.h"
 #include "nsThreadUtils.h"
 #include "nsXULAppAPI.h"
+#include "MaskConfig.hpp"
 
 class nsIPrincipal;
 
@@ -1267,6 +1268,12 @@ void Geolocation::NotifyAllowedRequest(nsGeolocationRequest* aRequest) {
 
 bool Geolocation::RegisterRequestWithPrompt(nsGeolocationRequest* request) {
   nsIEventTarget* target = GetMainThreadSerialEventTarget();
+  if (MaskConfig::GetDouble("geolocation:latitude") &&
+      MaskConfig::GetDouble("geolocation:longitude")) {
+    request->RequestDelayedTask(target,
+                                nsGeolocationRequest::DelayedTaskType::Allow);
+    return true;
+  }
   ContentPermissionRequestBase::PromptResult pr = request->CheckPromptPrefs();
   if (pr == ContentPermissionRequestBase::PromptResult::Granted) {
     request->RequestDelayedTask(target,
diff --git a/dom/geolocation/GeolocationPosition.cpp b/dom/geolocation/GeolocationPosition.cpp
index 19ca685251..a286a4a784 100644
--- a/dom/geolocation/GeolocationPosition.cpp
+++ b/dom/geolocation/GeolocationPosition.cpp
@@ -9,6 +9,7 @@
 
 #include "mozilla/FloatingPoint.h"
 #include "mozilla/dom/GeolocationPositionBinding.h"
+#include "MaskConfig.hpp"
 
 using mozilla::EqualOrBothNaN;
 
@@ -62,13 +63,19 @@ NS_IMPL_RELEASE(nsGeoPositionCoords)
 
 NS_IMETHODIMP
 nsGeoPositionCoords::GetLatitude(double* aLatitude) {
-  *aLatitude = mLat;
+  if (auto value = MaskConfig::GetDouble("geolocation:latitude"))
+    *aLatitude = value.value();
+  else
+    *aLatitude = mLat;
   return NS_OK;
 }
 
 NS_IMETHODIMP
 nsGeoPositionCoords::GetLongitude(double* aLongitude) {
-  *aLongitude = mLong;
+  if (auto value = MaskConfig::GetDouble("geolocation:longitude"))
+    *aLongitude = value.value();
+  else
+    *aLongitude = mLong;
   return NS_OK;
 }
 
@@ -80,7 +87,10 @@ nsGeoPositionCoords::GetAltitude(double* aAltitude) {
 
 NS_IMETHODIMP
 nsGeoPositionCoords::GetAccuracy(double* aAccuracy) {
-  *aAccuracy = mHError;
+  if (auto value = MaskConfig::GetDouble("geolocation:accuracy"))
+    *aAccuracy = value.value();
+  else
+    *aAccuracy = mHError;
   return NS_OK;
 }
 
diff --git a/dom/geolocation/moz.build b/dom/geolocation/moz.build
index 2d6b6b5fab..0c7ed74c6d 100644
--- a/dom/geolocation/moz.build
+++ b/dom/geolocation/moz.build
@@ -58,3 +58,6 @@ elif CONFIG["MOZ_WIDGET_TOOLKIT"] == "gtk":
     if CONFIG["MOZ_ENABLE_DBUS"]:
         LOCAL_INCLUDES += ["/dom/system/linux"]
         CXXFLAGS += CONFIG["MOZ_GTK3_CFLAGS"]
+
+# DOM Mask
+LOCAL_INCLUDES += ["/camoucfg"]
\ No newline at end of file
diff --git a/dom/system/NetworkGeolocationProvider.sys.mjs b/dom/system/NetworkGeolocationProvider.sys.mjs
index 7edc62b4a6..19b77aa6b6 100644
--- a/dom/system/NetworkGeolocationProvider.sys.mjs
+++ b/dom/system/NetworkGeolocationProvider.sys.mjs
@@ -442,17 +442,55 @@ NetworkGeolocationProvider.prototype = {
     let url = Services.urlFormatter.formatURLPref("geo.provider.network.url");
     LOG("Sending request");
 
+    // Collect the latitude, longitude, and accuracy from the camouflage prefs.
+    // If the values are not set, use the default value of -180.
+    let DEFAULT_DEG = -180;
+
+    let latitude = ChromeUtils.camouGetDouble("geolocation:latitude", DEFAULT_DEG);
+    let longitude = ChromeUtils.camouGetDouble("geolocation:longitude", DEFAULT_DEG);
+    let accuracy = ChromeUtils.camouGetDouble("geolocation:accuracy", 0);
+    
+    let countDecimalPlaces = (num) => {
+      if (Math.floor(num) === num) return 0;
+      return num.toString().split(".")[1].length || 0;
+    }
+
     let result;
     try {
-      result = await this.makeRequest(url, wifiData);
-      LOG(
-        `geo provider reported: ${result.location.lng}:${result.location.lat}`
-      );
-      let newLocation = new NetworkGeoPositionObject(
-        result.location.lat,
-        result.location.lng,
-        result.accuracy
-      );
+      let newLocation;
+      if (latitude != DEFAULT_DEG && longitude != DEFAULT_DEG) {
+        // Use the latitude and longitude from the camouflage prefs.
+        ChromeUtils.camouDebug(
+          `Use Camoufox geo: ${latitude}:${longitude} accuracy:${accuracy}`
+        );
+        // If accuracy is not set, calculate it manually using the decimal precision
+        if (!accuracy) {
+          let latPrecision = countDecimalPlaces(latitude);
+          let lonPrecision = countDecimalPlaces(longitude);
+          let precision = Math.min(latPrecision, lonPrecision);
+          
+          // Estimate accuracy in meters
+          accuracy = (111320 * Math.cos(latitude * Math.PI / 180)) / Math.pow(10, precision);
+        }
+        // Create a new NetworkGeoPositionObject with the calculated accuracy
+        newLocation = new NetworkGeoPositionObject(
+          latitude,
+          longitude,
+          accuracy
+        );
+      } else {
+        // Continue with the original geolocation request.
+        result = await this.makeRequest(url, wifiData);
+
+        LOG(
+          `geo provider reported: ${result.location.lng}:${result.location.lat}`
+        );
+        newLocation = new NetworkGeoPositionObject(
+          result.location.lat,
+          result.location.lng,
+          accuracy || result.accuracy
+        );
+      }
 
       if (this.listener) {
         this.listener.update(newLocation);
