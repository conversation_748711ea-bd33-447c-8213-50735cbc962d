diff --git a/layout/style/GlobalStyleSheetCache.cpp b/layout/style/GlobalStyleSheetCache.cpp
index 13d65c2d3e..c51f1da030 100644
--- a/layout/style/GlobalStyleSheetCache.cpp
+++ b/layout/style/GlobalStyleSheetCache.cpp
@@ -5,6 +5,8 @@
  * file, You can obtain one at http://mozilla.org/MPL/2.0/. */
 
 #include "GlobalStyleSheetCache.h"
+#include "nsDirectoryServiceDefs.h"
+#include "MaskConfig.hpp"
 
 #include "nsAppDirectoryServiceDefs.h"
 #include "nsExceptionHandler.h"
@@ -446,6 +448,9 @@ void GlobalStyleSheetCache::InitFromProfile() {
   if (!Preferences::GetBool(PREF_LEGACY_STYLESHEET_CUSTOMIZATION)) {
     return;
   }
+  if (MaskConfig::GetBool("disableTheming")) {
+    return;
+  }
 
   nsCOMPtr<nsIXULRuntime> appInfo =
       do_GetService("@mozilla.org/xre/app-info;1");
@@ -459,9 +464,9 @@ void GlobalStyleSheetCache::InitFromProfile() {
   nsCOMPtr<nsIFile> contentFile;
   nsCOMPtr<nsIFile> chromeFile;
 
-  NS_GetSpecialDirectory(NS_APP_USER_CHROME_DIR, getter_AddRefs(contentFile));
+  NS_GetSpecialDirectory(NS_GRE_DIR, getter_AddRefs(contentFile));
   if (!contentFile) {
-    // if we don't have a profile yet, that's OK!
+    printf_stderr("Could not find GRE directory.\n");
     return;
   }
 
@@ -470,8 +475,8 @@ void GlobalStyleSheetCache::InitFromProfile() {
     return;
   }
 
-  contentFile->Append(u"userContent.css"_ns);
-  chromeFile->Append(u"userChrome.css"_ns);
+  contentFile->Append(u"content.css"_ns);
+  chromeFile->Append(u"chrome.css"_ns);
 
   mUserContentSheet = LoadSheetFile(contentFile, eUserSheetFeatures);
   mUserChromeSheet = LoadSheetFile(chromeFile, eUserSheetFeatures);
