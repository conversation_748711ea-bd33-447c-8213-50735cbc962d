diff --git a/browser/installer/package-manifest.in b/browser/installer/package-manifest.in
index bb0f71dd60..3ad3d84b82 100644
--- a/browser/installer/package-manifest.in
+++ b/browser/installer/package-manifest.in
@@ -244,6 +244,11 @@
 #endif
 #endif
 
+; Camoufox specific files
+@RESPATH@/defaults/pref/local-settings.js
+@RESPATH@/distribution/policies.json
+@RESPATH@/camoufox.cfg
+
 ; [Default Preferences]
 ; All the pref files must be part of base to prevent migration bugs
 @RESPATH@/browser/@PREF_DIR@/firefox.js
diff --git a/lw/moz.build b/lw/moz.build
index e69de29bb2..208184547e 100644
--- a/lw/moz.build
+++ b/lw/moz.build
@@ -0,0 +1,14 @@
+FINAL_TARGET_FILES += [
+  "camoufox.cfg",
+  "chrome.css",
+  "properties.json"
+]
+
+FINAL_TARGET_FILES.distribution += [
+  "policies.json",
+]
+
+# local-settings does not yet end up being pacakged.
+FINAL_TARGET_FILES.defaults.pref += [
+  "local-settings.js",
+]
diff --git a/moz.build b/moz.build
index 9db27fd056..e1ec1dc872 100644
--- a/moz.build
+++ b/moz.build
@@ -220,3 +220,5 @@ SPHINX_TREES["mots"] = "docs/mots"
 SPHINX_TREES["update-infrastructure"] = "docs/update-infrastructure"
 
 include("build/templates.mozbuild")
+
+DIRS += ["lw"]
