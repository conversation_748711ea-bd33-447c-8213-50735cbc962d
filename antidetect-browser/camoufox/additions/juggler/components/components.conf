# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.
 
Classes = [
    # Juggler
    {
        "cid": "{f7a74a33-e2ab-422d-b022-4fb213dd2639}",
        "contract_ids": ["@mozilla.org/remote/juggler;1"],
        "categories": {
            "command-line-handler": "m-remote",
            "profile-after-change": "Juggler",
        },
        "jsm": "chrome://juggler/content/components/Juggler.js",
        "constructor": "JugglerFactory",
    },
]
 
