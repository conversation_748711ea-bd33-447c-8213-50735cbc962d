<style>
  body, html { margin: 0; padding: 0; }
  @keyframes move {
    from { marign-left: 0; }
    to   { margin-left: 100px; }
  }
</style>
<script>
function addButton() {
  const button = document.createElement('button');
  button.textContent = 'Click me';
  button.style.animation = '3s linear move';
  button.style.animationIterationCount = 'infinite';
  button.addEventListener('click', () => window.clicked = true);
  document.body.appendChild(button);
}

function stopButton(remove) {
  const button = document.querySelector('button');
  button.style.marginLeft = button.getBoundingClientRect().left + 'px';
  button.style.animation = '';
  if (remove)
    button.remove();
}

let x = 0;
function jump() {
  x += 300;
  const button = document.querySelector('button');
  button.style.marginLeft = x + 'px';
}

function startJumping() {
  x = 0;
  const moveIt = () => {
    jump();
    requestAnimationFrame(moveIt);
  };
  setInterval(jump, 0);
  moveIt();
}
</script>
