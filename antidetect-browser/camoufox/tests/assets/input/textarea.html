<!DOCTYPE html>
<html>
  <head>
    <title>Textarea test</title>
  </head>
  <body>
    <textarea spellcheck="false"></textarea>
    <input></input>
    <div contenteditable="true"></div>
    <div class="plain">Plain div</div>
    <script src='mouse-helper.js'></script>
    <script>
      window.result = '';
      let textarea = document.querySelector('textarea');
      textarea.addEventListener('input', () => result = textarea.value, false);
      let input = document.querySelector('input');
      input.addEventListener('input', () => result = input.value, false);
    </script>
  </body>
</html>
