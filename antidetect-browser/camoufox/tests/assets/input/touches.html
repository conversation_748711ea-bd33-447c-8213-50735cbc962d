<!DOCTYPE html>
<html>
  <head>
    <title>Touch test</title>
  </head>
  <body>
    <script src="mouse-helper.js"></script>
    <button onclick="clicked();">Click target</button>
    <script>
      window.result = [];
      const button = document.querySelector('button');
      button.style.height = '200px';
      button.style.width = '200px';
      button.focus();
      button.addEventListener('touchstart', event => {
        log('Touchstart:', ...Array.from(event.changedTouches).map(touch => touch.identifier));
      });
      button.addEventListener('touchend', event => {
        log('Touchend:', ...Array.from(event.changedTouches).map(touch => touch.identifier));
      });
      button.addEventListener('touchmove', event => {
        log('Touchmove:', ...Array.from(event.changedTouches).map(touch => touch.identifier));
      });
      function log(...args) {
        console.log.apply(console, args);
        result.push(args.join(' '));
      }
      function getResult() {
        let temp = result;
        result = [];
        return temp;
      }
    </script>
  </body>
</html>
