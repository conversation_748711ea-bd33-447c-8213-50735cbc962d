<style>
div:not(.mouse-helper) {
  margin: 0em;
  padding: 2em;
}
#source {
  color: blue;
  border: 1px solid black;
}
#target {
  border: 1px solid black;
}
</style>

<script>

function dragstart_handler(ev) {
  ev.currentTarget.style.border = "dashed";
  ev.dataTransfer.setData("text/plain", ev.target.id);
}

function dragover_handler(ev) {
  ev.preventDefault();
}

function drop_handler(ev) {
  console.log("Drop");
  ev.preventDefault();
  var data = ev.dataTransfer.getData("text");
  ev.target.appendChild(document.getElementById(data));
}
</script>

<body>
  <div>
    <p id="source" ondragstart="dragstart_handler(event);" draggable="true">
      Select this element, drag it to the Drop Zone and then release the selection to move the element.</p>
  </div>
  <div id="target" ondrop="drop_handler(event);" ondragover="dragover_handler(event);">Drop Zone</div>
</body>
